import { Modu<PERSON> } from '@nestjs/common';
import { PuppeteerModule } from 'nestjs-puppeteer';
import { CpasService } from './cpas.service';
import { CpasController } from './cpas.controller';
import { InventoryModule } from '../inventory/inventory.module';
import { S3Module } from '../s3/s3.module';

@Module({
  imports: [
    PuppeteerModule.forFeature(['intelligence']),
    InventoryModule,
    S3Module,
  ],
  controllers: [CpasController],
  providers: [CpasService],
  exports: [CpasService],
})
export class CpasModule {}
