import * as fs from 'fs';
import * as path from 'path';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectPage } from 'nestjs-puppeteer';
import { Page } from 'puppeteer';
import {
  processAndSaveCSVFile,
  isDownloadAllowed,
  getCSVJson,
  getLastDownload,
  parseCSVFile,
  writeCSVFile,
} from '../shared/utils/file.utils';
import {
  getCSVJsonFromS3,
  processAndSaveCSVToS3,
  saveCSVToS3,
} from '../shared/utils/s3.utils';
import { validateCPAsCampaignFile } from '../shared/utils/cpas.utils';
import { InventoryService } from '../inventory/inventory.service';
import { S3Service } from '../s3/s3.service';

@Injectable()
export class CpasService {
  private readonly logger = new Logger('CpasService');
  private readonly CPAS_FILE = 'cpas.csv';
  private readonly HISTORY_URL =
    'https://intelligence.trivago.com/history?tab=CPA';
  private readonly CAMPAIGNS_URL = 'https://intelligence.trivago.com/campaigns';
  private readonly CPAS_REQUEST_URL =
    'https://intelligence.trivago.com/v1/bidding/source-file?jobId';
  private readonly CPAS_DROPDOWN_SELECTOR =
    'select[class*="c-history-toolbar"]';
  private readonly CPAS_CALENDAR_SELECTOR = '.c-calendar-selector';
  private readonly CPAS_CALENDAR_STARTDATE_SELECTOR =
    '.c-datePicker .c-datePicker__input';
  private readonly CPAS_CALENDAR_APPLY_BUTTON_SELECTOR = '.c-datePicker button';
  private readonly CPAS_DROPDOWN_OPTION_SELECTOR =
    'div[class*="history-event-file"] > button';
  private readonly CPAS_DOWNLOAD_BUTTON_SELECTOR =
    'div[class*="history-event-files_downloadMenu"] li';

  constructor(
    @InjectPage('intelligence') private readonly page: Page,
    private readonly inventoryService: InventoryService,
    private readonly s3Service: S3Service,
  ) {}

  getCPAsLastUpdate = async () => {
    try {
      // Try to get last update from S3 first
      const s3FileExists = await this.s3Service.csvFileExists(this.CPAS_FILE);

      if (s3FileExists) {
        const fileInfo = await this.s3Service.getCSVFileInfo(this.CPAS_FILE);
        return fileInfo.lastModified;
      }

      // Fallback to local file system
      return getLastDownload(this.CPAS_FILE);
    } catch (err) {
      this.logger.error(`Error getting CPAs last update: ${err.message}`);
      throw new InternalServerErrorException('Failed to get last update info');
    }
  };

  async getCPAs() {
    try {
      // Try to get from S3 first
      const s3FileExists = await this.s3Service.csvFileExists(this.CPAS_FILE);

      if (s3FileExists) {
        // const shouldDownload = await isS3DownloadAllowed(
        //   this.s3Service,
        //   this.CPAS_FILE,
        // );

        // if (!shouldDownload) {
        const cpas = await getCSVJsonFromS3(this.s3Service, this.CPAS_FILE);
        return cpas;
        // }
      }

      // Fallback to local file system if S3 file doesn't exist or is old
      const cpasPath = path.join(__dirname, `../../../files/${this.CPAS_FILE}`);

      if (fs.existsSync(cpasPath)) {
        if (isDownloadAllowed(this.CPAS_FILE) || !fs.existsSync(cpasPath)) {
          await processAndSaveCSVFile(
            null,
            fs.readFileSync(cpasPath),
            this.CPAS_FILE,
          );
        }

        const cpas = await getCSVJson(this.CPAS_FILE);

        // Also upload to S3 for future use
        try {
          const csvContent = fs.readFileSync(cpasPath, 'utf-8');
          await this.s3Service.uploadCSVFile(this.CPAS_FILE, csvContent);
          this.logger.log(`Uploaded ${this.CPAS_FILE} to S3 as backup`);
        } catch (s3Error) {
          this.logger.warn(`Failed to upload to S3: ${s3Error.message}`);
        }

        return cpas;
      }

      throw new Error('No CPAs file found locally or in S3');
    } catch (err) {
      this.logger.error(`Error getting CPAs: ${err.message}`);
      throw new InternalServerErrorException('Failed to get CPAs');
    }
  }

  async fetchCPAs() {
    const downloadsPath = path.join(__dirname, `../downloads`);

    try {
      let logged = false;

      while (!logged) {
        try {
          await Promise.all([
            this.page.waitForSelector(this.CPAS_DROPDOWN_SELECTOR),
            this.page.goto(this.HISTORY_URL, {
              waitUntil: 'domcontentloaded',
            }),
          ]);

          logged = true;
        } catch (err) {
          // Handle login if needed
        }
      }

      await this.page.select(
        this.CPAS_DROPDOWN_SELECTOR,
        'CAMPAIGN_ALLOCATION',
      );

      const calendarSelector = await this.page.$(this.CPAS_CALENDAR_SELECTOR);
      await calendarSelector?.click();

      const startDateInput = await this.page.$(
        this.CPAS_CALENDAR_STARTDATE_SELECTOR,
      );

      await startDateInput?.click({ clickCount: 3 });

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);

      const formattedDate = yesterday.toLocaleDateString('en-GB');

      await startDateInput?.type(formattedDate);

      const applyButton = await this.page.$(
        this.CPAS_CALENDAR_APPLY_BUTTON_SELECTOR,
      );
      await applyButton?.click();

      await this.page.waitForSelector(this.CPAS_DROPDOWN_OPTION_SELECTOR);

      const dropdownOption = await this.page.$(
        this.CPAS_DROPDOWN_OPTION_SELECTOR,
      );
      await dropdownOption?.click();

      const downloadButton = await this.page.$(
        this.CPAS_DOWNLOAD_BUTTON_SELECTOR,
      );
      await downloadButton?.click();

      const fileRequest = await this.page.waitForResponse(async (response) =>
        response.url().includes(this.CPAS_REQUEST_URL),
      );

      const fileRequestUrl = fileRequest.url();
      const urlParams = new URLSearchParams(fileRequestUrl.split('?')[1]);
      const jobId = urlParams.get('jobId');

      await new Promise((resolver) => setTimeout(resolver, 5000));
      const file = fs.readFileSync(path.join(downloadsPath, `${jobId}.csv`));

      // Save to local file system (existing functionality)
      await processAndSaveCSVFile(file, null, this.CPAS_FILE);

      // Also save to S3
      try {
        await processAndSaveCSVToS3(this.s3Service, file, this.CPAS_FILE);
        this.logger.log(`Successfully uploaded ${this.CPAS_FILE} to S3`);
      } catch (s3Error) {
        this.logger.error(`Failed to upload to S3: ${s3Error.message}`);
        // Don't throw here, as local save was successful
      }
    } catch (err) {
      this.logger.error(`Error fetching CPAs: ${err.message}`);
      throw new InternalServerErrorException(
        "Falha ao baixar arquivo de CPA's!",
      );
    }
  }

  async updateCPAFile(defaultCampaign: string) {
    try {
      const inventory = await this.inventoryService.getInventory();

      const cpasFilePath = path.join(__dirname, '../files', this.CPAS_FILE);

      const cpasData = await parseCSVFile(cpasFilePath, ',');

      const cpasMap = new Map();
      cpasData.forEach((cpa) => {
        cpasMap.set(cpa.partner_reference, cpa);
      });

      const updatedCpas = [];

      inventory.forEach((hotel) => {
        const existingCpa = cpasMap.get(hotel.partnerReference);
        if (existingCpa) {
          updatedCpas.push(existingCpa);
        } else {
          updatedCpas.push({
            partner_reference: hotel.partnerReference,
            locale: 'BR',
            campaign: defaultCampaign,
          });
        }
      });

      // Save to local file system
      await writeCSVFile(cpasFilePath, updatedCpas);

      const contentToAppend = updatedCpas
        .map((cpa) => `${cpa.partner_reference},${cpa.locale},${cpa.campaign}`)
        .join('\n');

      fs.appendFileSync(cpasFilePath, contentToAppend);

      // Also save to S3
      try {
        await saveCSVToS3(this.s3Service, this.CPAS_FILE, updatedCpas);
        this.logger.log(
          `Successfully uploaded updated ${this.CPAS_FILE} to S3`,
        );
      } catch (s3Error) {
        this.logger.error(
          `Failed to upload updated CPAs to S3: ${s3Error.message}`,
        );
        // Don't throw here, as local save was successful
      }
    } catch (err) {
      this.logger.error(`Error updating CPA file: ${err.message}`);
      throw new InternalServerErrorException('Failed to update CPA file');
    }
  }

  async uploadNewCPAFile(filePath: string) {
    try {
      let logged = false;

      while (!logged) {
        try {
          await Promise.all([
            this.page.waitForSelector('button[data-qa="upload-bids"]'),
            this.page.goto(this.CAMPAIGNS_URL, {
              waitUntil: 'domcontentloaded',
            }),
          ]);

          logged = true;
        } catch (err) {
          // Handle login if needed
        }
      }

      const uploadDropdownButton = await this.page.$(
        'button[data-qa="upload-bids"]',
      );
      await uploadDropdownButton.click();

      const inputFile = await this.page.$(
        'label[data-qa="file-uploader-CAMPAIGN_ALLOCATION"] input[class="c-file-uploader__input"]',
      );
      await inputFile.uploadFile(filePath);
    } catch (err) {
      throw new InternalServerErrorException(
        "Falha ao fazer upload do novo CPA's!",
      );
    }
  }

  async validateCampaignFile() {
    try {
      await validateCPAsCampaignFile();
      return true;
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }
}
