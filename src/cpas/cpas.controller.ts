import {
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  Put,
  Res,
  StreamableFile,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import { CpasService } from './cpas.service';
import { InventoryService } from '../inventory/inventory.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { createReadStream } from 'fs';
import { UpdateCPAFile } from '../shared/types';
import * as path from 'path';
import { CacheInterceptor, CacheKey, CacheTTL } from '@nestjs/cache-manager';

@Controller('cpas')
export class CpasController {
  constructor(
    private readonly cpasService: CpasService,
    private readonly inventoryService: InventoryService,
  ) {}

  @CacheTTL(3600000)
  @Get()
  @CacheKey('cpas')
  @ApiTags('CPA')
  @ApiOperation({ summary: 'Get CPA file in JSON format' })
  @UseInterceptors(CacheInterceptor)
  async getCPAs() {
    return await this.cpasService.getCPAs();
  }

  @Get('/fetch')
  @ApiTags('CPA')
  @ApiOperation({ summary: 'Update server CPA file with Trivago Intelligence' })
  async fetchCPAs() {
    return await this.cpasService.fetchCPAs();
  }

  @Get('/info')
  @ApiTags('CPA')
  @ApiOperation({ summary: 'Return current server CPAs file info' })
  async getCPAsInfo() {
    try {
      const cpas = await this.cpasService.getCPAs();
      const inventory = await this.inventoryService.getInventory();

      return {
        hotelsWithCPA: cpas.length,
        hotelsWithoutCPA: inventory.length - cpas.length,
        lastUpdate: this.cpasService.getCPAsLastUpdate(),
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  @ApiTags('CPA')
  @Put('/update')
  @ApiOperation({ summary: 'Match new CPA file hotels with server inventory' })
  async getUpdatedCPA(
    @Body() params: UpdateCPAFile,
    @Res({ passthrough: true }) res: Response,
  ) {
    await this.cpasService.updateCPAFile(params.defaultCampaign);

    const filePath = path.join(__dirname, '..', 'files', 'cpas.csv');
    const file = createReadStream(filePath);

    const isValid = await this.cpasService.validateCampaignFile();

    if (isValid) {
      await this.cpasService.uploadNewCPAFile(filePath);
    }

    res.set({
      'Content-Type': 'text/csv',
      'Content-Disposition': 'attachment; filename="cpas.csv"',
    });

    return new StreamableFile(file);
  }

  @Get('/validate')
  @ApiTags('CPA')
  @ApiOperation({ summary: 'Return if CPAs Campaign file is valid' })
  async validateCampaignFile() {
    try {
      await this.cpasService.validateCampaignFile();
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }
}
