import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { S3Service } from './s3.service';

describe('S3Service', () => {
  let service: S3Service;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        AWS_REGION: 'us-east-1',
        AWS_ACCESS_KEY_ID: 'test-access-key',
        AWS_SECRET_ACCESS_KEY: 'test-secret-key',
        AWS_S3_BUCKET_NAME: 'test-bucket',
      };
      return config[key] || defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3Service,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<S3Service>(S3Service);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize with correct configuration', () => {
    expect(configService.get).toHaveBeenCalledWith('AWS_REGION', 'us-east-1');
    expect(configService.get).toHaveBeenCalledWith('AWS_ACCESS_KEY_ID');
    expect(configService.get).toHaveBeenCalledWith('AWS_SECRET_ACCESS_KEY');
    expect(configService.get).toHaveBeenCalledWith('AWS_S3_BUCKET_NAME');
  });

  it('should throw error if required credentials are missing', async () => {
    const mockConfigServiceMissingCreds = {
      get: jest.fn((key: string, defaultValue?: any) => {
        const config = {
          AWS_REGION: 'us-east-1',
          AWS_ACCESS_KEY_ID: undefined,
          AWS_SECRET_ACCESS_KEY: undefined,
          AWS_S3_BUCKET_NAME: undefined,
        };
        return config[key] || defaultValue;
      }),
    };

    expect(() => {
      const module = Test.createTestingModule({
        providers: [
          S3Service,
          {
            provide: ConfigService,
            useValue: mockConfigServiceMissingCreds,
          },
        ],
      }).compile();
    }).rejects.toThrow();
  });
});
