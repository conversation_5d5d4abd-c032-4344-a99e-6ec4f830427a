export interface S3Config {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
}

export interface S3UploadOptions {
  key: string;
  body: Buffer | string;
  contentType?: string;
  metadata?: Record<string, string>;
}

export interface S3DownloadOptions {
  key: string;
}

export interface S3FileInfo {
  key: string;
  size: number;
  lastModified: Date;
  etag: string;
}

export interface S3ListOptions {
  prefix?: string;
  maxKeys?: number;
}

export interface S3UploadResult {
  key: string;
  location: string;
  etag: string;
  bucket: string;
}
