import {
  S3<PERSON><PERSON>,
  PutO<PERSON>Command,
  GetObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Config,
  S3UploadOptions,
  S3DownloadOptions,
  S3FileInfo,
  S3ListOptions,
  S3UploadResult,
} from './interfaces/s3.interface';

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;

  constructor(private readonly configService: ConfigService) {
    const config: S3Config = {
      region: this.configService.get<string>('AWS_REGION', 'us-east-1'),
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      bucketName: this.configService.get<string>('AWS_S3_BUCKET_NAME'),
    };

    if (!config.accessKeyId || !config.secretAccessKey || !config.bucketName) {
      throw new Error(
        'AWS credentials and bucket name must be provided via environment variables',
      );
    }

    this.bucketName = config.bucketName;
    this.s3Client = new S3Client({
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });

    this.logger.log(`S3Service initialized with bucket: ${this.bucketName}`);
  }

  async uploadFile(options: S3UploadOptions): Promise<S3UploadResult> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: options.key,
        Body: options.body,
        ContentType: options.contentType || 'application/octet-stream',
        Metadata: options.metadata,
      });

      const result = await this.s3Client.send(command);

      this.logger.log(`File uploaded successfully: ${options.key}`);

      return {
        key: options.key,
        location: `https://${this.bucketName}.s3.amazonaws.com/${options.key}`,
        etag: result.ETag,
        bucket: this.bucketName,
      };
    } catch (error) {
      console.log(error);
      this.logger.error(`Failed to upload file ${options.key}:`, error);
      throw new InternalServerErrorException(
        `Failed to upload file to S3: ${error.message}`,
      );
    }
  }

  async downloadFile(options: S3DownloadOptions): Promise<Buffer> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: options.key,
      });

      const result = await this.s3Client.send(command);

      if (!result.Body) {
        throw new Error('No body in S3 response');
      }

      const chunks: Uint8Array[] = [];
      const stream = result.Body as any;

      for await (const chunk of stream) {
        chunks.push(chunk);
      }

      const buffer = Buffer.concat(chunks);
      this.logger.log(`File downloaded successfully: ${options.key}`);

      return buffer;
    } catch (error) {
      this.logger.error(`Failed to download file ${options.key}:`, error);
      throw new InternalServerErrorException(
        `Failed to download file from S3: ${error.message}`,
      );
    }
  }

  async fileExists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      if (
        error.name === 'NotFound' ||
        error.$metadata?.httpStatusCode === 404
      ) {
        return false;
      }
      this.logger.error(`Error checking if file exists ${key}:`, error);
      throw new InternalServerErrorException(
        `Failed to check file existence: ${error.message}`,
      );
    }
  }

  async getFileInfo(key: string): Promise<S3FileInfo> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const result = await this.s3Client.send(command);

      return {
        key,
        size: result.ContentLength || 0,
        lastModified: result.LastModified || new Date(),
        etag: result.ETag || '',
      };
    } catch (error) {
      this.logger.error(`Failed to get file info ${key}:`, error);
      throw new InternalServerErrorException(
        `Failed to get file info from S3: ${error.message}`,
      );
    }
  }

  async listFiles(options: S3ListOptions = {}): Promise<S3FileInfo[]> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: options.prefix,
        MaxKeys: options.maxKeys || 1000,
      });

      const result = await this.s3Client.send(command);

      if (!result.Contents) {
        return [];
      }

      return result.Contents.map((object) => ({
        key: object.Key || '',
        size: object.Size || 0,
        lastModified: object.LastModified || new Date(),
        etag: object.ETag || '',
      }));
    } catch (error) {
      this.logger.error('Failed to list files:', error);
      throw new InternalServerErrorException(
        `Failed to list files from S3: ${error.message}`,
      );
    }
  }

  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`File deleted successfully: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key}:`, error);
      throw new InternalServerErrorException(
        `Failed to delete file from S3: ${error.message}`,
      );
    }
  }

  async uploadCSVFile(
    fileName: string,
    csvContent: string,
  ): Promise<S3UploadResult> {
    return this.uploadFile({
      key: `intelligence/${fileName}`,
      body: csvContent,
      contentType: 'text/csv',
      metadata: {
        uploadedAt: new Date().toISOString(),
        fileType: 'csv',
      },
    });
  }

  async downloadCSVFile(fileName: string): Promise<string> {
    const buffer = await this.downloadFile({
      key: `intelligence/${fileName}`,
    });
    return buffer.toString('utf-8');
  }

  async csvFileExists(fileName: string): Promise<boolean> {
    return this.fileExists(`intelligence/${fileName}`);
  }

  async getCSVFileInfo(fileName: string): Promise<S3FileInfo> {
    return this.getFileInfo(`intelligence/${fileName}`);
  }
}
