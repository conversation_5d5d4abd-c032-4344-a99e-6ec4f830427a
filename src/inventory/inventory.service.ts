import * as fs from 'fs';
import * as path from 'path';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectPage } from 'nestjs-puppeteer';
import { Page } from 'puppeteer';
import {
  processAndSaveCSVFile,
  isDownloadAllowed,
  extractArtifactPath,
  getCSVJson,
  getLastDownload,
} from '../shared/utils/file.utils';
import {
  getCSVJsonFromS3,
  processAndSaveCSVToS3,
} from '../shared/utils/s3.utils';
import { Inventory } from '../shared/types';
import { S3Service } from '../s3/s3.service';

@Injectable()
export class InventoryService {
  private readonly logger = new Logger('InventoryService');
  private readonly INVENTORY_FILE = 'inventory.csv';
  private readonly ACTIVE_INVENTORY_URL =
    'https://intelligence.trivago.com/reports/inventory-matched-active-report?reportDefinitionId=5&isCombined=false';
  private readonly INVENTORY_REQUEST_URL =
    'https://intelligence.trivago.com/v1/reports/artifacts/3620/download';
  private readonly INVENTORY_DOWNLOAD_BUTTON_SELECTOR = 'td > .e-button';

  constructor(
    @InjectPage('intelligence') private readonly page: Page,
    private readonly s3Service: S3Service,
  ) {}

  async getInventory() {
    try {
      // Try to get from S3 first
      const s3FileExists = await this.s3Service.csvFileExists(
        this.INVENTORY_FILE,
      );

      if (s3FileExists) {
        // const shouldDownload = await isS3DownloadAllowed(
        //   this.s3Service,
        //   this.INVENTORY_FILE,
        // );

        // if (!shouldDownload) {
        const inventory = await getCSVJsonFromS3(
          this.s3Service,
          this.INVENTORY_FILE,
        );
        return inventory;
        // }
      }

      // Fallback to local file system if S3 file doesn't exist or is old
      const inventoryPath = path.join(
        __dirname,
        `../../../files/${this.INVENTORY_FILE}`,
      );

      if (fs.existsSync(inventoryPath)) {
        if (
          isDownloadAllowed(this.INVENTORY_FILE) ||
          !fs.existsSync(inventoryPath)
        ) {
          await processAndSaveCSVFile(
            null,
            fs.readFileSync(inventoryPath),
            this.INVENTORY_FILE,
          );
        }

        const inventory = await getCSVJson(this.INVENTORY_FILE);

        // Also upload to S3 for future use
        try {
          const csvContent = fs.readFileSync(inventoryPath, 'utf-8');
          await this.s3Service.uploadCSVFile(this.INVENTORY_FILE, csvContent);
          this.logger.log(`Uploaded ${this.INVENTORY_FILE} to S3 as backup`);
        } catch (s3Error) {
          this.logger.warn(`Failed to upload to S3: ${s3Error.message}`);
        }

        return inventory;
      }

      throw new Error('No inventory file found locally or in S3');
    } catch (err) {
      this.logger.error(`Error getting inventory: ${err.message}`);
      throw new InternalServerErrorException('Failed to get inventory');
    }
  }

  async fetchInventory() {
    const downloadsPath = path.join(__dirname, `../downloads`);

    try {
      try {
        await this.page.goto(this.ACTIVE_INVENTORY_URL, {
          waitUntil: 'domcontentloaded',
        });
        await this.page.waitForSelector(
          this.INVENTORY_DOWNLOAD_BUTTON_SELECTOR,
        );
      } catch (err) {
        console.log('unable to goto', err, this.page.url());
      }

      const inventoryButton = await this.page.$(
        this.INVENTORY_DOWNLOAD_BUTTON_SELECTOR,
      );

      await inventoryButton?.click();

      const fileRequest = await this.page.waitForResponse(async (response) =>
        response.url().includes(this.INVENTORY_REQUEST_URL),
      );

      const fileRequestUrl = fileRequest.url();
      const fileName = extractArtifactPath(fileRequestUrl)
        .split('/')
        .reverse()[0];

      await new Promise((resolver) => setTimeout(resolver, 5000));
      const file = fs.readFileSync(path.join(downloadsPath, fileName));

      // Save to local file system (existing functionality)
      await processAndSaveCSVFile(file, null, this.INVENTORY_FILE);

      // Also save to S3
      try {
        await processAndSaveCSVToS3(this.s3Service, file, this.INVENTORY_FILE);
        this.logger.log(`Successfully uploaded ${this.INVENTORY_FILE} to S3`);
      } catch (s3Error) {
        this.logger.error(`Failed to upload to S3: ${s3Error.message}`);
        // Don't throw here, as local save was successful
      }
    } catch (err) {
      this.logger.error(`Error fetching inventory: ${err.message}`);
      throw new InternalServerErrorException(
        'Falha ao baixar arquivo de inventário!',
      );
    }
  }

  getInventoryLastUpdate = async () => {
    try {
      // Try to get last update from S3 first
      const s3FileExists = await this.s3Service.csvFileExists(
        this.INVENTORY_FILE,
      );

      if (s3FileExists) {
        const fileInfo = await this.s3Service.getCSVFileInfo(
          this.INVENTORY_FILE,
        );
        return fileInfo.lastModified;
      }

      // Fallback to local file system
      return getLastDownload(this.INVENTORY_FILE);
    } catch (err) {
      this.logger.error(`Error getting inventory last update: ${err.message}`);
      throw new InternalServerErrorException('Failed to get last update info');
    }
  };

  async getHotelByHotelId(id: string) {
    try {
      const inventory: Inventory = await this.getInventory();

      const hotel = inventory.find((hotel) => hotel.partnerReference === id);
      return hotel;
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }

  async getHotelByTrivagoId(id: string) {
    try {
      const inventory: Inventory = await this.getInventory();

      const hotel = inventory.find((hotel) => hotel.trivagoItemID === id);
      return hotel;
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }
}
