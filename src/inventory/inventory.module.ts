import { Module } from '@nestjs/common';
import { PuppeteerModule } from 'nestjs-puppeteer';
import { InventoryService } from './inventory.service';
import { InventoryController } from './inventory.controller';
import { S3Module } from '../s3/s3.module';

@Module({
  imports: [PuppeteerModule.forFeature(['intelligence']), S3Module],
  controllers: [InventoryController],
  providers: [InventoryService],
  exports: [InventoryService],
})
export class InventoryModule {}
