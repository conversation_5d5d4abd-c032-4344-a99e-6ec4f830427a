import {
  Controller,
  Get,
  InternalServerErrorException,
  Param,
  UseInterceptors,
} from '@nestjs/common';
import { InventoryService } from './inventory.service';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { <PERSON>acheInterceptor, CacheKey, CacheTTL } from '@nestjs/cache-manager';

@Controller('inventory')
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @CacheTTL(86400000)
  @Get()
  @ApiTags('Inventory')
  @CacheKey('inventory')
  @ApiOperation({ summary: 'Get inventory file in JSON format' })
  @UseInterceptors(CacheInterceptor)
  async getInventory() {
    return await this.inventoryService.getInventory();
  }

  @Get('/fetch')
  @ApiTags('Inventory')
  @ApiOperation({
    summary: 'Update server inventory file with Trivago Intelligence',
  })
  async updateInventory() {
    try {
      await this.inventoryService.fetchInventory();
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }

  @Get('/info')
  @ApiTags('Inventory')
  @ApiOperation({ summary: 'Return current server inventory file info' })
  async getInventoryInfo() {
    try {
      const inventory = await this.inventoryService.getInventory();

      return {
        hotels: inventory.length,
        lastUpdate: this.inventoryService.getInventoryLastUpdate(),
      };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  @CacheTTL(86400000)
  @Get('/filters')
  @CacheKey('inventory-filters')
  @ApiTags('Inventory')
  @ApiOperation({ summary: 'Return current server inventory file filters' })
  @UseInterceptors(CacheInterceptor)
  async getInventoryFilters() {
    try {
      const inventory = await this.inventoryService.getInventory();

      const filters = Object.keys(inventory[0]);
      return filters;
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  @ApiTags('Inventory')
  @ApiParam({ name: 'hotelId' })
  @Get('/hotel/otid/:hotelId')
  @ApiOperation({ summary: 'Get inventory hotel by OurTrip Hotel ID' })
  async getHotelByHotelId(@Param('hotelId') hotelId) {
    return await this.inventoryService.getHotelByHotelId(hotelId);
  }

  @ApiTags('Inventory')
  @ApiParam({ name: 'trivagoId' })
  @Get('/hotel/tid/:trivagoId')
  @ApiOperation({ summary: 'Get inventory hotel by Trivago ID' })
  async getHotelByTrivagoId(@Param('trivagoId') trivagoId) {
    return await this.inventoryService.getHotelByTrivagoId(trivagoId);
  }
}
