import { Module } from '@nestjs/common';
import { PuppeteerModule } from 'nestjs-puppeteer';
import { CampaignService } from './campaign.service';
import { CampaignController } from './campaign.controller';
import { S3Module } from '../s3/s3.module';

@Module({
  imports: [PuppeteerModule.forFeature(['intelligence']), S3Module],
  controllers: [CampaignController],
  providers: [CampaignService],
  exports: [CampaignService],
})
export class CampaignModule {}
