import * as fs from 'fs';
import * as path from 'path';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectPage } from 'nestjs-puppeteer';
import { Page } from 'puppeteer';
import {
  processAndSaveCSVFile,
  isDownloadAllowed,
  getCSVJson,
} from '../shared/utils/file.utils';
import {
  getCSVJsonFromS3,
  processAndSaveCSVToS3,
} from '../shared/utils/s3.utils';
import { IntelligenceCampaigns } from '../shared/types';
import { extractBiddingData } from '../shared/utils/campaign.utils';
import { S3Service } from '../s3/s3.service';

@Injectable()
export class CampaignService {
  private readonly logger = new Logger('CampaignService');
  private readonly CAMPAIGNS_FILE = 'campaigns.csv';
  private readonly CAMPAIGNS_URL = 'https://intelligence.trivago.com/campaigns';

  constructor(
    @InjectPage('intelligence') private readonly page: Page,
    private readonly s3Service: S3Service,
  ) {}

  async getCampaigns() {
    try {
      // Try to get from S3 first
      const s3FileExists = await this.s3Service.csvFileExists(
        this.CAMPAIGNS_FILE,
      );

      if (s3FileExists) {
        // const shouldDownload = await isS3DownloadAllowed(
        //   this.s3Service,
        //   this.CAMPAIGNS_FILE,
        // );

        // if (!shouldDownload) {
        const intelligenceCampaigns: IntelligenceCampaigns =
          await getCSVJsonFromS3(this.s3Service, this.CAMPAIGNS_FILE);
        const campaigns = intelligenceCampaigns
          .map((campaign) => {
            if (campaign.biddingType != 'Manual')
              return extractBiddingData(campaign);
          })
          .filter((campaign) => campaign != null);

        return campaigns;
        // }
      }

      const campaignsPath = path.join(
        __dirname,
        `../../files/${this.CAMPAIGNS_FILE}`,
      );

      if (fs.existsSync(campaignsPath)) {
        if (
          isDownloadAllowed(this.CAMPAIGNS_FILE) ||
          !fs.existsSync(campaignsPath)
        ) {
          await processAndSaveCSVFile(
            null,
            fs.readFileSync(campaignsPath),
            this.CAMPAIGNS_FILE,
          );
        }

        const intelligenceCampaigns: IntelligenceCampaigns = await getCSVJson(
          this.CAMPAIGNS_FILE,
        );

        // Also upload to S3 for future use
        try {
          const csvContent = fs.readFileSync(campaignsPath, 'utf-8');
          await this.s3Service.uploadCSVFile(this.CAMPAIGNS_FILE, csvContent);
          this.logger.log(`Uploaded ${this.CAMPAIGNS_FILE} to S3 as backup`);
        } catch (s3Error) {
          this.logger.warn(`Failed to upload to S3: ${s3Error.message}`);
        }

        const campaigns = intelligenceCampaigns
          .map((campaign) => {
            if (campaign.biddingType != 'Manual')
              return extractBiddingData(campaign);
          })
          .filter((campaign) => campaign != null);

        return campaigns;
      }

      throw new Error('No campaigns file found locally or in S3');
    } catch (err) {
      this.logger.error(`Error getting campaigns: ${err.message}`);
      throw new InternalServerErrorException('Failed to get campaigns');
    }
  }

  async fetchCampaigns() {
    try {
      let logged = false;

      while (!logged) {
        try {
          await Promise.all([
            this.page.waitForSelector(
              'button[data-qa*="export-report-button"]',
            ),
            this.page.goto('https://intelligence.trivago.com/campaigns', {
              waitUntil: 'domcontentloaded',
            }),
          ]);

          logged = true;
        } catch (err) {
          // Handle login if needed
        }
      }

      const dropdownButton = await this.page.$(
        'button[data-qa*="export-report-button"]',
      );
      await dropdownButton?.click();

      const downloadButton = await this.page.$(
        'li[data-qa*="dropdown-option-0"]',
      );
      await downloadButton?.click();

      const file = await this.page.waitForResponse(async (response) =>
        response
          .url()
          .includes(
            'https://intelligence.trivago.com/v1/analytics/export/download?view=campaigns',
          ),
      );

      await new Promise((resolver) => setTimeout(resolver, 5000));

      // Save to local file system (existing functionality)
      await processAndSaveCSVFile(file, null, this.CAMPAIGNS_FILE);

      // Also save to S3
      try {
        const fileBuffer = await file.buffer();
        await processAndSaveCSVToS3(
          this.s3Service,
          fileBuffer,
          this.CAMPAIGNS_FILE,
        );
        this.logger.log(`Successfully uploaded ${this.CAMPAIGNS_FILE} to S3`);
      } catch (s3Error) {
        this.logger.error(`Failed to upload to S3: ${s3Error.message}`);
        // Don't throw here, as local save was successful
      }
    } catch (err) {
      this.logger.error(`Error fetching campaigns: ${err.message}`);
      throw new InternalServerErrorException('Falha ao baixar campanhas!');
    }
  }
}
