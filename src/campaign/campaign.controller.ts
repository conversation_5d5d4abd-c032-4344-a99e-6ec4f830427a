import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { CampaignService } from './campaign.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CacheInterceptor, CacheKey, CacheTTL } from '@nestjs/cache-manager';

@Controller('campaigns')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @CacheTTL(3600000)
  @Get()
  @CacheKey('campaigns')
  @ApiTags('Campaign')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get campaign file in JSON format' })
  async getCampaigns() {
    return await this.campaignService.getCampaigns();
  }

  @Get('/fetch')
  @ApiTags('Campaign')
  @ApiOperation({
    summary: 'Update server campaign file with Trivago Intelligence',
  })
  async updateCampaigns() {
    return await this.campaignService.fetchCampaigns();
  }
}
