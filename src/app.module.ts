import { Modu<PERSON> } from '@nestjs/common';
import { PuppeteerModule } from 'nestjs-puppeteer';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import { InventoryModule } from './inventory/inventory.module';
import { CampaignModule } from './campaign/campaign.module';
import { CpasModule } from './cpas/cpas.module';
import { AuthModule } from './auth/auth.module';
import { AuthService } from './auth/auth.service';
import { S3Module } from './s3/s3.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    PuppeteerModule.forRoot({
      headless: true,
      args: [
        '--lang=pt-BR,br',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
      ],
    }),
    PuppeteerModule.forFeature(['intelligence']),
    CacheModule.register({
      isGlobal: true,
    }),
    AuthModule,
    S3Module,
    InventoryModule,
    CampaignModule,
    CpasModule,
  ],
})
export class AppModule {
  constructor(private readonly authService: AuthService) {
    // this.authService.init();
  }
}
