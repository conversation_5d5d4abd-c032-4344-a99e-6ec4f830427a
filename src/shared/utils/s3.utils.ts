import * as csv from 'csv-parser';
import { Readable, Writable } from 'stream';
import { InternalServerErrorException } from '@nestjs/common';
import { S3Service } from '../../s3/s3.service';

export const processCSVFromS3 = async (
  s3Service: S3Service,
  fileName: string,
  dataArray: any[],
): Promise<void> => {
  try {
    const csvContent = await s3Service.downloadCSVFile(fileName);
    await processCSVFromString(csvContent, dataArray);
  } catch (error) {
    throw new InternalServerErrorException(
      `Failed to process CSV from S3: ${error.message}`,
    );
  }
};

export const processCSVFromString = (
  csvContent: string,
  dataArray: any[],
): Promise<void> => {
  return new Promise<void>((resolve, reject) => {
    const processCSV = new Writable({
      objectMode: true,
      write(row, _, callback) {
        dataArray.push(row);
        callback();
      },
    });

    const csvStream = new Readable({
      read() {
        this.push(csvContent);
        this.push(null);
      },
    });

    csvStream
      .pipe(
        csv({
          separator: identifyDelimiterFromString(csvContent),
          mapHeaders: ({ header }) => toCamelCase(header),
        }),
      )
      .pipe(processCSV)
      .on('finish', resolve)
      .on('error', reject);
  });
};

export const saveCSVToS3 = async (
  s3Service: S3Service,
  fileName: string,
  data: any[],
): Promise<void> => {
  try {
    if (!data || data.length === 0) {
      throw new Error('No data provided to save');
    }

    const csvHeaders = Object.keys(data[0]).join(',');
    const csvRows = data.map((row) => Object.values(row).join(','));
    const csvContent = [csvHeaders, ...csvRows].join('\n');

    await s3Service.uploadCSVFile(fileName, csvContent);
  } catch (error) {
    throw new InternalServerErrorException(
      `Failed to save CSV to S3: ${error.message}`,
    );
  }
};

export const getCSVJsonFromS3 = async (
  s3Service: S3Service,
  fileName: string,
): Promise<any[]> => {
  try {
    const dataArray = [];
    await processCSVFromS3(s3Service, fileName, dataArray);
    return dataArray;
  } catch (error) {
    throw new InternalServerErrorException(
      `Failed to get CSV JSON from S3: ${error.message}`,
    );
  }
};

export const isS3DownloadAllowed = async (
  s3Service: S3Service,
  fileName: string,
): Promise<boolean> => {
  try {
    const exists = await s3Service.csvFileExists(fileName);
    if (!exists) return true;

    const fileInfo = await s3Service.getCSVFileInfo(fileName);
    const now = new Date();
    const diff = now.getTime() - fileInfo.lastModified.getTime();
    // Allow download if file is older than 24 hours
    return diff > 24 * 60 * 60 * 1000;
  } catch (error) {
    // If we can't check, allow download
    return true;
  }
};

export const processAndSaveCSVToS3 = async (
  s3Service: S3Service,
  fileBuffer: Buffer,
  fileName: string,
): Promise<void> => {
  try {
    const csvContent = fileBuffer.toString('utf-8');
    await s3Service.uploadCSVFile(fileName, csvContent);
  } catch (error) {
    throw new InternalServerErrorException(
      `Failed to process and save CSV to S3: ${error.message}`,
    );
  }
};

const detectDelimiter = (line: string): string => {
  const delimiters = [',', ';', '\t', '|'];
  const counts: Record<string, number> = {};

  delimiters.forEach((delimiter) => {
    counts[delimiter] = line.split(delimiter).length - 1;
  });

  return Object.keys(counts).reduce((a, b) => (counts[a] > counts[b] ? a : b));
};

const identifyDelimiterFromString = (csvContent: string): string => {
  const firstLine = csvContent.split('\n')[0];
  return detectDelimiter(firstLine);
};

const toCamelCase = (str: string): string => {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) => {
      if (+match === 0) return '';
      return index === 0 ? match.toLowerCase() : match.toUpperCase();
    })
    .replace(/\s+/g, '');
};

export const uploadBufferToS3 = async (
  s3Service: S3Service,
  buffer: Buffer,
  fileName: string,
  contentType?: string,
): Promise<void> => {
  try {
    await s3Service.uploadFile({
      key: `files/${fileName}`,
      body: buffer,
      contentType: contentType || 'application/octet-stream',
      metadata: {
        uploadedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    throw new InternalServerErrorException(
      `Failed to upload buffer to S3: ${error.message}`,
    );
  }
};

export const downloadBufferFromS3 = async (
  s3Service: S3Service,
  fileName: string,
): Promise<Buffer> => {
  try {
    return await s3Service.downloadFile({
      key: `files/${fileName}`,
    });
  } catch (error) {
    throw new InternalServerErrorException(
      `Failed to download buffer from S3: ${error.message}`,
    );
  }
};
